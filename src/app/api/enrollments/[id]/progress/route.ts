import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Enrollment } from '@/models/Enrollment';
import { Course } from '@/models/Course';
import { requireAuth } from '@/lib/auth-middleware';

export async function PATCH(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { params } = context;
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const userId = req.user?.id;
      const body = await request.json();
      const { lessonId, timeSpent, moduleIndex, lessonIndex } = body;

      // Find the enrollment
      const enrollment = await Enrollment.findOne({
        _id: params.id,
        userId: userId,
        isActive: true,
      });

      if (!enrollment) {
        return NextResponse.json(
          { success: false, error: 'Enrollment not found' },
          { status: 404 }
        );
      }

      // Get course details
      const course = await Course.findById(enrollment.courseId);
      if (!course) {
        return NextResponse.json(
          { success: false, error: 'Course not found' },
          { status: 404 }
        );
      }

      // Update progress
      const updates: Record<string, unknown> = {
        lastAccessedAt: new Date(),
        'progress.currentModule': moduleIndex,
        'progress.currentLesson': lessonIndex,
      };

      // Add lesson to completed lessons if not already completed
      if (lessonId && !enrollment.progress.completedLessons.includes(lessonId)) {
        updates.$addToSet = { 'progress.completedLessons': lessonId };
      }

      // Add time spent
      if (timeSpent && timeSpent > 0) {
        updates.$inc = { 'progress.timeSpent': timeSpent };
      }

      // Update enrollment
      const updatedEnrollment = await Enrollment.findByIdAndUpdate(
        params.id,
        updates,
        { new: true }
      );

      // Calculate completion percentage
      const totalLessons = course.modules.reduce(
        (total, module) => total + module.lessons.length,
        0
      );
      const completedLessons = updatedEnrollment.progress.completedLessons.length;
      const completionPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;

      // Check if course is completed
      if (completionPercentage === 100 && !updatedEnrollment.completedAt) {
        updatedEnrollment.completedAt = new Date();
        updatedEnrollment.progress.completedModules = course.modules.length;
        await updatedEnrollment.save();

        // Generate certificate if available
        if (course.certification.available) {
          try {
            await fetch(`${process.env.NEXTAUTH_URL}/api/certificates/generate`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${req.headers.authorization}`,
              },
              body: JSON.stringify({
                courseId: course._id,
                type: 'course_completion',
              }),
            });
          } catch (error) {
            console.error('Error generating certificate:', error);
          }
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          enrollment: updatedEnrollment,
          completionPercentage,
          isCompleted: completionPercentage === 100,
        },
      });
    } catch (error) {
      console.error('Error updating progress:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update progress' },
        { status: 500 }
      );
    }
  });
}

export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { params } = context;
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const userId = req.user?.id;

      // Find the enrollment with course details
      const enrollment = await Enrollment.findOne({
        _id: params.id,
        userId: userId,
        isActive: true,
      }).populate('courseId', 'title modules certification');

      if (!enrollment) {
        return NextResponse.json(
          { success: false, error: 'Enrollment not found' },
          { status: 404 }
        );
      }

      const course = enrollment.courseId as typeof Course.prototype;

      // Calculate detailed progress
      const totalLessons = course.modules.reduce(
        (total: number, module: typeof Course.prototype.modules[0]) => total + module.lessons.length,
        0
      );
      const completedLessons = enrollment.progress.completedLessons.length;
      const completionPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;

      // Calculate module progress
      const moduleProgress = course.modules.map((module: typeof Course.prototype.modules[0], moduleIndex: number) => {
        const moduleLessons = module.lessons.length;
        const completedModuleLessons = enrollment.progress.completedLessons.filter(
          (lessonId: string) => lessonId.startsWith(`${moduleIndex}-`)
        ).length;

        return {
          moduleIndex,
          title: module.title,
          totalLessons: moduleLessons,
          completedLessons: completedModuleLessons,
          completionPercentage: moduleLessons > 0 ? Math.round((completedModuleLessons / moduleLessons) * 100) : 0,
          isCompleted: completedModuleLessons === moduleLessons,
        };
      });

      // Calculate estimated time remaining
      const averageTimePerLesson = 15; // minutes
      const remainingLessons = totalLessons - completedLessons;
      const estimatedTimeRemaining = remainingLessons * averageTimePerLesson;

      return NextResponse.json({
        success: true,
        data: {
          enrollment,
          progress: {
            totalLessons,
            completedLessons,
            completionPercentage,
            timeSpent: enrollment.progress.timeSpent,
            timeSpentHours: Math.round(enrollment.progress.timeSpent / 60 * 10) / 10,
            estimatedTimeRemaining,
            estimatedTimeRemainingHours: Math.round(estimatedTimeRemaining / 60 * 10) / 10,
            currentModule: enrollment.progress.currentModule,
            currentLesson: enrollment.progress.currentLesson,
            isCompleted: completionPercentage === 100,
            moduleProgress,
          },
          course: {
            title: course.title,
            totalModules: course.modules.length,
            certificationAvailable: course.certification.available,
          },
        },
      });
    } catch (error) {
      console.error('Error fetching progress:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch progress' },
        { status: 500 }
      );
    }
  });
}
