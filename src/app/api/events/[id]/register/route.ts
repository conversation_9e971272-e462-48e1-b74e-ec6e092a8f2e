import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { Event, EventRegistration } from '@/models/Event';
import { User } from '@/models/User';
import { SMSService } from '@/lib/sms';

export async function POST(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { params } = context;
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const event = await Event.findById(params.id);
    if (!event) {
      return NextResponse.json(
        { success: false, error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check if event is published and registration is open
    if (event.status !== 'published') {
      return NextResponse.json(
        { success: false, error: 'Event is not available for registration' },
        { status: 400 }
      );
    }

    // Check registration deadline
    if (event.registration.deadline && new Date() > event.registration.deadline) {
      return NextResponse.json(
        { success: false, error: 'Registration deadline has passed' },
        { status: 400 }
      );
    }

    // Check if event has started
    if (new Date() >= event.schedule.startDate) {
      return NextResponse.json(
        { success: false, error: 'Event has already started' },
        { status: 400 }
      );
    }

    // Check if user is already registered
    const existingRegistration = await EventRegistration.findOne({
      eventId: params.id,
      userId: session.user.id,
    });

    if (existingRegistration) {
      return NextResponse.json(
        { success: false, error: 'Already registered for this event' },
        { status: 409 }
      );
    }

    // Check capacity
    if (event.registration.maxAttendees &&
        event.registration.currentAttendees >= event.registration.maxAttendees) {
      return NextResponse.json(
        { success: false, error: 'Event is full' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { specialRequests } = body;

    // Create registration
    const registration = new EventRegistration({
      eventId: params.id,
      userId: session.user.id,
      status: event.registration.requiresApproval ? 'registered' : 'confirmed',
      specialRequests,
      paymentStatus: event.registration.isFree ? undefined : 'pending',
    });

    await registration.save();

    // Update event attendee count
    await Event.findByIdAndUpdate(params.id, {
      $inc: { 'registration.currentAttendees': 1 },
    });

    // Get user details for notifications
    const user = await User.findById(session.user.id);

    // Send confirmation SMS
    try {
      if (user?.phone) {
        await SMSService.sendSMS(
          user.phone,
          'event_registration',
          {
            userName: user.name,
            eventTitle: event.title,
            eventDate: event.schedule.startDate.toLocaleDateString(),
            eventTime: event.schedule.startDate.toLocaleTimeString(),
            location: event.location.type === 'online' ? 'Online' : event.location.venue,
          }
        );
      }
    } catch (smsError) {
      console.error('Error sending registration SMS:', smsError);
    }

    // Populate registration with user details
    await registration.populate('userId', 'name email');

    return NextResponse.json({
      success: true,
      data: registration,
      message: event.registration.requiresApproval
        ? 'Registration submitted for approval'
        : 'Successfully registered for event',
    }, { status: 201 });
  } catch (error) {
    console.error('Error registering for event:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to register for event' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { params } = context;
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const registration = await EventRegistration.findOne({
      eventId: params.id,
      userId: session.user.id,
    });

    if (!registration) {
      return NextResponse.json(
        { success: false, error: 'Registration not found' },
        { status: 404 }
      );
    }

    const event = await Event.findById(params.id);
    if (!event) {
      return NextResponse.json(
        { success: false, error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check if event has started (can't cancel after event starts)
    if (new Date() >= event.schedule.startDate) {
      return NextResponse.json(
        { success: false, error: 'Cannot cancel registration after event has started' },
        { status: 400 }
      );
    }

    // Update registration status instead of deleting
    registration.status = 'cancelled';
    await registration.save();

    // Decrease attendee count
    await Event.findByIdAndUpdate(params.id, {
      $inc: { 'registration.currentAttendees': -1 },
    });

    // Send cancellation SMS
    try {
      const user = await User.findById(session.user.id);
      if (user?.phone) {
        await SMSService.sendSMS(
          user.phone,
          'event_cancellation',
          {
            userName: user.name,
            eventTitle: event.title,
            eventDate: event.schedule.startDate.toLocaleDateString(),
          }
        );
      }
    } catch (smsError) {
      console.error('Error sending cancellation SMS:', smsError);
    }

    return NextResponse.json({
      success: true,
      message: 'Registration cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling event registration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to cancel registration' },
      { status: 500 }
    );
  }
}
